{"messages": {"Test1": {"enums": {"TestEnum": {"no": 0, "yes": 1}}, "fields": {"int32": {"tag": 1, "type": "int32"}, "uint32": {"tag": 2, "type": "uint32"}, "sint32": {"tag": 3, "type": "sint32"}, "int64": {"tag": 4, "type": "int64"}, "uint64": {"tag": 5, "type": "uint64"}, "sint64": {"tag": 6, "type": "sint64"}, "bool": {"tag": 7, "type": "bool"}, "enum": {"tag": 8, "type": "TestEnum"}, "fixed64": {"tag": 9, "type": "fixed64"}, "sfixed64": {"tag": 10, "type": "sfixed64"}, "double": {"tag": 11, "type": "double"}, "string": {"tag": 12, "type": "string"}, "bytes": {"tag": 13, "type": "bytes"}, "fixed32": {"tag": 14, "type": "fixed32"}, "sfixed32": {"tag": 15, "type": "sfixed32"}, "float": {"tag": 16, "type": "float"}}}, "Test2": {"fields": {"ints": {"tag": 1, "type": "int32", "rule": "repeated"}, "strings": {"tag": 2, "type": "string", "rule": "repeated"}}}, "Test3": {"fields": {"test": {"tag": 1, "type": "Test1"}, "tests": {"tag": 2, "type": "Test1", "rule": "repeated"}}}}}