{"version": 3, "file": "W3CBaggagePropagator.js", "sourceRoot": "", "sources": ["../../../../src/baggage/propagation/W3CBaggagePropagator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAGL,WAAW,GAIZ,MAAM,oBAAoB,CAAC;AAE5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EACL,cAAc,EACd,uBAAuB,EACvB,4BAA4B,EAC5B,gCAAgC,GACjC,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAE7E;;;;;GAKG;AACH,MAAM,OAAO,oBAAoB;IAC/B,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC;YAAE,OAAO;QACrD,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC;aAClC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,IAAI,gCAAgC,CAAC;QACzD,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,EAAE,4BAA4B,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;SAClD;IACH,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACxD,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC;YAC3C,CAAC,CAAC,WAAW,CAAC;QAChB,IAAI,CAAC,aAAa;YAAE,OAAO,OAAO,CAAC;QACnC,MAAM,OAAO,GAAiC,EAAE,CAAC;QACjD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,OAAO,CAAC;SAChB;QACD,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3D,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACpB,MAAM,OAAO,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACzC,IAAI,OAAO,EAAE;gBACX,MAAM,YAAY,GAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC5D,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACpB,YAAY,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;iBAC1C;gBACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,OAAO,OAAO,CAAC;SAChB;QACD,OAAO,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,cAAc,CAAC,CAAC;IAC1B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  BaggageEntry,\n  Context,\n  propagation,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n} from '@opentelemetry/api';\n\nimport { isTracingSuppressed } from '../../trace/suppress-tracing';\nimport {\n  BAGGAGE_HEADER,\n  BAGGAGE_ITEMS_SEPARATOR,\n  BAGGAGE_MAX_NAME_VALUE_PAIRS,\n  BAGGAGE_MAX_PER_NAME_VALUE_PAIRS,\n} from '../constants';\nimport { getKeyPairs, parsePairKeyValue, serializeKeyPairs } from '../utils';\n\n/**\n * Propagates {@link Baggage} through Context format propagation.\n *\n * Based on the Baggage specification:\n * https://w3c.github.io/baggage/\n */\nexport class W3CBaggagePropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const baggage = propagation.getBaggage(context);\n    if (!baggage || isTracingSuppressed(context)) return;\n    const keyPairs = getKeyPairs(baggage)\n      .filter((pair: string) => {\n        return pair.length <= BAGGAGE_MAX_PER_NAME_VALUE_PAIRS;\n      })\n      .slice(0, BAGGAGE_MAX_NAME_VALUE_PAIRS);\n    const headerValue = serializeKeyPairs(keyPairs);\n    if (headerValue.length > 0) {\n      setter.set(carrier, BAGGAGE_HEADER, headerValue);\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const headerValue = getter.get(carrier, BAGGAGE_HEADER);\n    const baggageString = Array.isArray(headerValue)\n      ? headerValue.join(BAGGAGE_ITEMS_SEPARATOR)\n      : headerValue;\n    if (!baggageString) return context;\n    const baggage: Record<string, BaggageEntry> = {};\n    if (baggageString.length === 0) {\n      return context;\n    }\n    const pairs = baggageString.split(BAGGAGE_ITEMS_SEPARATOR);\n    pairs.forEach(entry => {\n      const keyPair = parsePairKeyValue(entry);\n      if (keyPair) {\n        const baggageEntry: BaggageEntry = { value: keyPair.value };\n        if (keyPair.metadata) {\n          baggageEntry.metadata = keyPair.metadata;\n        }\n        baggage[keyPair.key] = baggageEntry;\n      }\n    });\n    if (Object.entries(baggage).length === 0) {\n      return context;\n    }\n    return propagation.setBaggage(context, propagation.createBaggage(baggage));\n  }\n\n  fields(): string[] {\n    return [BAGGAGE_HEADER];\n  }\n}\n"]}