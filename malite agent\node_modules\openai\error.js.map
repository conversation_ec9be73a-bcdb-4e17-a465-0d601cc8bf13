{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["src/error.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AAEtF,oCAA8C;AAE9C,MAAa,WAAY,SAAQ,KAAK;CAAG;AAAzC,kCAAyC;AAEzC,MAAa,QAIX,SAAQ,WAAW;IAcnB,YAAY,MAAe,EAAE,KAAa,EAAE,OAA2B,EAAE,OAAiB;QACxF,KAAK,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,MAAM,IAAI,GAAG,KAA4B,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,MAA0B,EAAE,KAAU,EAAE,OAA2B;QAC5F,MAAM,GAAG,GACP,KAAK,EAAE,OAAO,CAAC,CAAC;YACd,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC;gBACjC,KAAK,CAAC,OAAO;gBACf,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC;YACjC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBAC/B,CAAC,CAAC,OAAO,CAAC;QAEZ,IAAI,MAAM,IAAI,GAAG,EAAE;YACjB,OAAO,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC;SAC3B;QACD,IAAI,MAAM,EAAE;YACV,OAAO,GAAG,MAAM,wBAAwB,CAAC;SAC1C;QACD,IAAI,GAAG,EAAE;YACP,OAAO,GAAG,CAAC;SACZ;QACD,OAAO,0BAA0B,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,QAAQ,CACb,MAA0B,EAC1B,aAAiC,EACjC,OAA2B,EAC3B,OAA4B;QAE5B,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE;YACvB,OAAO,IAAI,kBAAkB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,IAAA,kBAAW,EAAC,aAAa,CAAC,EAAE,CAAC,CAAC;SAC/E;QAED,MAAM,KAAK,GAAI,aAAqC,EAAE,CAAC,OAAO,CAAC,CAAC;QAEhE,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC7D;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACjE;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACnE;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC3D;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC3D;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,wBAAwB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACtE;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC5D;QAED,IAAI,MAAM,IAAI,GAAG,EAAE;YACjB,OAAO,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACjE;QAED,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;CACF;AAlGD,4BAkGC;AAED,MAAa,iBAAkB,SAAQ,QAAyC;IAC9E,YAAY,EAAE,OAAO,KAA2B,EAAE;QAChD,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,IAAI,sBAAsB,EAAE,SAAS,CAAC,CAAC;IAC5E,CAAC;CACF;AAJD,8CAIC;AAED,MAAa,kBAAmB,SAAQ,QAAyC;IAC/E,YAAY,EAAE,OAAO,EAAE,KAAK,EAA+D;QACzF,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,IAAI,mBAAmB,EAAE,SAAS,CAAC,CAAC;QACvE,gEAAgE;QAChE,aAAa;QACb,IAAI,KAAK;YAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAChC,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,yBAA0B,SAAQ,kBAAkB;IAC/D,YAAY,EAAE,OAAO,KAA2B,EAAE;QAChD,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,oBAAoB,EAAE,CAAC,CAAC;IACtD,CAAC;CACF;AAJD,8DAIC;AAED,MAAa,eAAgB,SAAQ,QAAsB;CAAG;AAA9D,0CAA8D;AAE9D,MAAa,mBAAoB,SAAQ,QAAsB;CAAG;AAAlE,kDAAkE;AAElE,MAAa,qBAAsB,SAAQ,QAAsB;CAAG;AAApE,sDAAoE;AAEpE,MAAa,aAAc,SAAQ,QAAsB;CAAG;AAA5D,sCAA4D;AAE5D,MAAa,aAAc,SAAQ,QAAsB;CAAG;AAA5D,sCAA4D;AAE5D,MAAa,wBAAyB,SAAQ,QAAsB;CAAG;AAAvE,4DAAuE;AAEvE,MAAa,cAAe,SAAQ,QAAsB;CAAG;AAA7D,wCAA6D;AAE7D,MAAa,mBAAoB,SAAQ,QAAyB;CAAG;AAArE,kDAAqE;AAErE,MAAa,uBAAwB,SAAQ,WAAW;IACtD;QACE,KAAK,CAAC,kEAAkE,CAAC,CAAC;IAC5E,CAAC;CACF;AAJD,0DAIC;AAED,MAAa,8BAA+B,SAAQ,WAAW;IAC7D;QACE,KAAK,CAAC,oFAAoF,CAAC,CAAC;IAC9F,CAAC;CACF;AAJD,wEAIC"}