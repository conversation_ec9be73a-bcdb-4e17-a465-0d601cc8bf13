(function(){/*
 Long.js (c) 2013 <PERSON> <<EMAIL>>
 Released under the Apache License, Version 2.0
 Derived from goog.math.Long from the Closure Library
 see: https://github.com/dcodeIO/Long.js for details
*/
var q=!0,r=!1;
(function(s){function b(a,b,d){a&&"object"===typeof a&&(b=a.high,d=a.unsigned,a=a.low);this.low=a|0;this.high=b|0;this.unsigned=!!d}var t={},u={};b.fromInt=function(a,c){var d;if(c){a>>>=0;if(0<=a&&256>a&&(d=u[a]))return d;d=new b(a,0>(a|0)?-1:0,q);0<=a&&256>a&&(u[a]=d)}else{a|=0;if(-128<=a&&128>a&&(d=t[a]))return d;d=new b(a,0>a?-1:0,r);-128<=a&&128>a&&(t[a]=d)}return d};b.fromNumber=function(a,c){c=!!c;return isNaN(a)||!isFinite(a)?b.ZERO:!c&&a<=-v?b.MIN_SIGNED_VALUE:c&&0>=a?b.MIN_UNSIGNED_VALUE:!c&&
a+1>=v?b.MAX_SIGNED_VALUE:c&&a>=w?b.MAX_UNSIGNED_VALUE:0>a?b.fromNumber(-a,r).negate():new b(a%l|0,a/l|0,c)};b.fromBits=function(a,c,d){return new b(a,c,d)};b.from28Bits=function(a,c,d,e){return b.fromBits(a|c<<28,c>>>4|d<<24,e)};b.fromString=function(a,c,d){if(0==a.length)throw Error("number format error: empty string");if("NaN"===a||"Infinity"===a||"+Infinity"===a||"-Infinity"===a)return b.ZERO;"number"===typeof c&&(d=c,c=r);d=d||10;if(2>d||36<d)throw Error("radix out of range: "+d);if("-"==a.charAt(0))return b.fromString(a.substring(1),
c,d).negate();if(0<=a.indexOf("-"))throw Error('number format error: interior "-" character: '+a);for(var e=b.fromNumber(Math.pow(d,8)),f=b.ZERO,g=0;g<a.length;g+=8){var k=Math.min(8,a.length-g),m=parseInt(a.substring(g,g+k),d);8>k?(k=b.fromNumber(Math.pow(d,k)),f=f.multiply(k).add(b.fromNumber(m))):(f=f.multiply(e),f=f.add(b.fromNumber(m)))}f.unsigned=c;return f};var l=4294967296,w=l*l,v=w/2,x=b.fromInt(16777216);b.ZERO=b.fromInt(0);b.UZERO=b.fromInt(0,q);b.ONE=b.fromInt(1);b.UONE=b.fromInt(1,q);
b.NEG_ONE=b.fromInt(-1);b.MAX_SIGNED_VALUE=b.fromBits(-1,2147483647,r);b.MAX_UNSIGNED_VALUE=b.fromBits(-1,-1,q);b.MAX_VALUE=b.MAX_SIGNED_VALUE;b.MIN_SIGNED_VALUE=b.fromBits(0,-2147483648,r);b.MIN_UNSIGNED_VALUE=b.fromBits(0,0,q);b.MIN_VALUE=b.MIN_SIGNED_VALUE;b.prototype.toInt=function(){return this.unsigned?this.low>>>0:this.low};b.prototype.toNumber=function(){return this.unsigned?(this.high>>>0)*l+(this.low>>>0):this.high*l+(this.low>>>0)};b.prototype.toString=function(a){a=a||10;if(2>a||36<a)throw Error("radix out of range: "+
a);if(this.isZero())return"0";var c;if(this.isNegative()){if(this.equals(b.MIN_SIGNED_VALUE)){c=b.fromNumber(a);var d=this.div(c);c=d.multiply(c).subtract(this);return d.toString(a)+c.toInt().toString(a)}return"-"+this.negate().toString(a)}d=b.fromNumber(Math.pow(a,6));c=this;for(var e="";;){var f=c.div(d),g=c.subtract(f.multiply(d)).toInt().toString(a);c=f;if(c.isZero())return g+e;for(;6>g.length;)g="0"+g;e=""+g+e}};b.prototype.getHighBits=function(){return this.high};b.prototype.getHighBitsUnsigned=
function(){return this.high>>>0};b.prototype.getLowBits=function(){return this.low};b.prototype.getLowBitsUnsigned=function(){return this.low>>>0};b.prototype.getNumBitsAbs=function(){if(this.isNegative())return this.equals(b.MIN_SIGNED_VALUE)?64:this.negate().getNumBitsAbs();for(var a=0!=this.high?this.high:this.low,c=31;0<c&&0==(a&1<<c);c--);return 0!=this.high?c+33:c+1};b.prototype.isZero=function(){return 0==this.high&&0==this.low};b.prototype.isNegative=function(){return!this.unsigned&&0>this.high};
b.prototype.isOdd=function(){return 1==(this.low&1)};b.prototype.equals=function(a){return this.unsigned!=a.unsigned&&this.high>>>31!=a.high>>>31?r:this.high==a.high&&this.low==a.low};b.prototype.notEquals=function(a){return!this.equals(a)};b.prototype.lessThan=function(a){return 0>this.compare(a)};b.prototype.lessThanOrEqual=function(a){return 0>=this.compare(a)};b.prototype.greaterThan=function(a){return 0<this.compare(a)};b.prototype.greaterThanOrEqual=function(a){return 0<=this.compare(a)};b.prototype.compare=
function(a){if(this.equals(a))return 0;var b=this.isNegative(),d=a.isNegative();return b&&!d?-1:!b&&d?1:this.unsigned?a.high>>>0>this.high>>>0||a.high==this.high&&a.low>>>0>this.low>>>0?-1:1:this.subtract(a).isNegative()?-1:1};b.prototype.negate=function(){return!this.unsigned&&this.equals(b.MIN_SIGNED_VALUE)?b.MIN_SIGNED_VALUE:this.not().add(b.ONE)};b.prototype.add=function(a){var c=this.high>>>16,d=this.high&65535,e=this.low>>>16,f=a.high>>>16,g=a.high&65535,k=a.low>>>16,m;m=0+((this.low&65535)+
(a.low&65535));a=0+(m>>>16);a+=e+k;e=0+(a>>>16);e+=d+g;d=0+(e>>>16);d=d+(c+f)&65535;return b.fromBits((a&65535)<<16|m&65535,d<<16|e&65535,this.unsigned)};b.prototype.subtract=function(a){return this.add(a.negate())};b.prototype.multiply=function(a){if(this.isZero()||a.isZero())return b.ZERO;if(this.equals(b.MIN_VALUE))return a.isOdd()?b.MIN_VALUE:b.ZERO;if(a.equals(b.MIN_VALUE))return this.isOdd()?b.MIN_VALUE:b.ZERO;if(this.isNegative())return a.isNegative()?this.negate().multiply(a.negate()):this.negate().multiply(a).negate();
if(a.isNegative())return this.multiply(a.negate()).negate();if(this.lessThan(x)&&a.lessThan(x))return b.fromNumber(this.toNumber()*a.toNumber(),this.unsigned);var c=this.high>>>16,d=this.high&65535,e=this.low>>>16,f=this.low&65535,g=a.high>>>16,k=a.high&65535,m=a.low>>>16;a=a.low&65535;var p,h,n,l;l=0+f*a;n=0+(l>>>16);n+=e*a;h=0+(n>>>16);n=(n&65535)+f*m;h+=n>>>16;n&=65535;h+=d*a;p=0+(h>>>16);h=(h&65535)+e*m;p+=h>>>16;h&=65535;h+=f*k;p+=h>>>16;h&=65535;p=p+(c*a+d*m+e*k+f*g)&65535;return b.fromBits(n<<
16|l&65535,p<<16|h,this.unsigned)};b.prototype.div=function(a){if(a.isZero())throw Error("division by zero");if(this.isZero())return this.unsigned?b.UZERO:b.ZERO;var c,d,e;if(this.equals(b.MIN_SIGNED_VALUE)){if(a.equals(b.ONE)||a.equals(b.NEG_ONE))return b.MIN_SIGNED_VALUE;if(a.equals(b.MIN_SIGNED_VALUE))return b.ONE;c=this.shiftRight(1).div(a).shiftLeft(1);if(c.equals(b.ZERO))return a.isNegative()?b.ONE:b.NEG_ONE;d=this.subtract(a.multiply(c));return e=c.add(d.div(a))}if(a.equals(b.MIN_SIGNED_VALUE))return this.unsigned?
b.UZERO:b.ZERO;if(this.isNegative())return a.isNegative()?this.negate().div(a.negate()):this.negate().div(a).negate();if(a.isNegative())return this.div(a.negate()).negate();e=b.ZERO;for(d=this;d.greaterThanOrEqual(a);){c=Math.max(1,Math.floor(d.toNumber()/a.toNumber()));for(var f=Math.ceil(Math.log(c)/Math.LN2),f=48>=f?1:Math.pow(2,f-48),g=b.fromNumber(c,this.unsigned),k=g.multiply(a);k.isNegative()||k.greaterThan(d);)c-=f,g=b.fromNumber(c,this.unsigned),k=g.multiply(a);g.isZero()&&(g=b.ONE);e=e.add(g);
d=d.subtract(k)}return e};b.prototype.modulo=function(a){return this.subtract(this.div(a).multiply(a))};b.prototype.not=function(){return b.fromBits(~this.low,~this.high,this.unsigned)};b.prototype.and=function(a){return b.fromBits(this.low&a.low,this.high&a.high,this.unsigned)};b.prototype.or=function(a){return b.fromBits(this.low|a.low,this.high|a.high,this.unsigned)};b.prototype.xor=function(a){return b.fromBits(this.low^a.low,this.high^a.high,this.unsigned)};b.prototype.shiftLeft=function(a){a&=
63;if(0==a)return this;var c=this.low;return 32>a?b.fromBits(c<<a,this.high<<a|c>>>32-a,this.unsigned):b.fromBits(0,c<<a-32,this.unsigned)};b.prototype.shiftRight=function(a){a&=63;if(0==a)return this;var c=this.high;return 32>a?b.fromBits(this.low>>>a|c<<32-a,c>>a,this.unsigned):b.fromBits(c>>a-32,0<=c?0:-1,this.unsigned)};b.prototype.shiftRightUnsigned=function(a){a&=63;if(0==a)return this;var c=this.high;return 32>a?b.fromBits(this.low>>>a|c<<32-a,c>>>a,this.unsigned):32==a?b.fromBits(c,0,this.unsigned):
b.fromBits(c>>>a-32,0,this.unsigned)};b.prototype.toSigned=function(){var a=this.clone();a.unsigned=r;return a};b.prototype.toUnsigned=function(){var a=this.clone();a.unsigned=q;return a};b.prototype.clone=function(){return new b(this.low,this.high,this.unsigned)};"undefined"!=typeof module&&module.exports?module.exports=b:"undefined"!=typeof define&&define.amd?define("Math/Long",[],function(){return b}):(s.dcodeIO||(s.dcodeIO={}),s.dcodeIO.Long=b)})(this);})();
