# Malite Agent - م<PERSON><PERSON><PERSON><PERSON> ذكي

مساعد ذكي مبني باستخدام Genkit و Google AI

## المتطلبات

- Node.js (الإصدار 18 أو أحدث)
- مفتاح API من Google AI Studio

## التثبيت

1. تثبيت التبعيات:
```bash
npm install
```

2. إعداد مفتاح API:
   - اذهب إلى [Google AI Studio](https://aistudio.google.com/app/apikey)
   - أنشئ مفتاح API جديد
   - انسخ المفتاح واستبدل `YOUR_NEW_API_KEY_HERE` في ملف `.env`

## الاستخدام

### تشغيل التطبيق:
```bash
npm start
```

### تشغيل في وضع التطوير (مع إعادة التحميل التلقائي):
```bash
npm run dev
```

## إعداد متغيرات البيئة

### الطريقة الأولى: ملف .env (الأفضل)
قم بتحرير ملف `.env` وضع مفتاح API الخاص بك:
```
GEMINI_API_KEY=مفتاح_API_الخاص_بك
```

### الطريقة الثانية: PowerShell (مؤقت)
```powershell
$env:GEMINI_API_KEY="مفتاح_API_الخاص_بك"
npm start
```

### الطريقة الثالثة: Command Prompt (مؤقت)
```cmd
set GEMINI_API_KEY=مفتاح_API_الخاص_بك
npm start
```

## الأمان

- لا تشارك مفتاح API علناً
- تأكد من إضافة `.env` إلى `.gitignore`
- أعد إنشاء مفتاح API إذا تم الكشف عنه

## الدعم

إذا واجهت أي مشاكل، تأكد من:
1. تثبيت Node.js بشكل صحيح
2. وجود مفتاح API صالح في ملف `.env`
3. الاتصال بالإنترنت
