import { googleAI } from '@genkit-ai/googleai';
import { genkit } from 'genkit';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// التحقق من وجود مفتاح API
if (!process.env.GEMINI_API_KEY) {
    console.error('خطأ: لم يتم العثور على GEMINI_API_KEY في متغيرات البيئة');
    console.error('يرجى إضافة مفتاح API في ملف .env');
    process.exit(1);
}

// تكوين Genkit
const ai = genkit({
    plugins: [googleAI()],
    model: googleAI.model('gemini-2.0-flash'),
});

async function main() {
    const { text } = await ai.generate('Hello, Gemini!');
    console.log(text);
}

main();
