{"version": 3, "file": "BrowserDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/BrowserDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,uCAAuC,EACvC,gCAAgC,EAChC,mCAAmC,GACpC,MAAM,qCAAqC,CAAC;AAE7C,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAG1C,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAEvC;;GAEG;AACH,MAAM,mBAAmB;IACvB,MAAM,CAAC,MAAgC;;QACrC,MAAM,SAAS,GACb,OAAO,SAAS,KAAK,WAAW;YAChC,CAAA,MAAA,MAAA,MAAM,CAAC,OAAO,0CAAE,QAAQ,0CAAE,IAAI,MAAK,SAAS,IAAI,+BAA+B;YAC/E,6DAA6D;YAC7D,kCAAkC;YAClC,CAAA,MAAA,MAAM,CAAC,GAAG,0CAAE,OAAO,MAAK,SAAS,CAAC,CAAC,mCAAmC;QACxE,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,QAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;QACD,MAAM,eAAe,GAAuB;YAC1C,CAAC,gCAAgC,CAAC,EAAE,SAAS;YAC7C,CAAC,uCAAuC,CAAC,EAAE,aAAa;YACxD,CAAC,mCAAmC,CAAC,EAAE,SAAS,CAAC,SAAS;SAC3D,CAAC;QACF,OAAO,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD;;;;;;OAMG;IACK,sBAAsB,CAC5B,eAAmC,EACnC,OAAiC;QAEjC,IAAI,eAAe,CAAC,mCAAmC,CAAC,KAAK,EAAE,EAAE;YAC/D,IAAI,CAAC,KAAK,CACR,qEAAqE,CACtE,CAAC;YACF,OAAO,QAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;aAAM;YACL,OAAO,IAAI,QAAQ,mBACd,eAAe,EAClB,CAAC;SACJ;IACH,CAAC;CACF;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION,\n  SEMRESATTRS_PROCESS_RUNTIME_NAME,\n  SEMRESATTRS_PROCESS_RUNTIME_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { DetectorSync, ResourceAttributes } from '../types';\nimport { diag } from '@opentelemetry/api';\nimport { ResourceDetectionConfig } from '../config';\nimport { IResource } from '../IResource';\nimport { Resource } from '../Resource';\n\n/**\n * BrowserDetectorSync will be used to detect the resources related to browser.\n */\nclass BrowserDetectorSync implements DetectorSync {\n  detect(config?: ResourceDetectionConfig): IResource {\n    const isBrowser =\n      typeof navigator !== 'undefined' &&\n      global.process?.versions?.node === undefined && // Node.js v21 adds `navigator`\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore don't have Bun types\n      global.Bun?.version === undefined; // Bun (bun.sh) defines `navigator`\n    if (!isBrowser) {\n      return Resource.empty();\n    }\n    const browserResource: ResourceAttributes = {\n      [SEMRESATTRS_PROCESS_RUNTIME_NAME]: 'browser',\n      [SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION]: 'Web Browser',\n      [SEMRESATTRS_PROCESS_RUNTIME_VERSION]: navigator.userAgent,\n    };\n    return this._getResourceAttributes(browserResource, config);\n  }\n  /**\n   * Validates process resource attribute map from process variables\n   *\n   * @param browserResource The un-sanitized resource attributes from process as key/value pairs.\n   * @param config: Config\n   * @returns The sanitized resource attributes.\n   */\n  private _getResourceAttributes(\n    browserResource: ResourceAttributes,\n    _config?: ResourceDetectionConfig\n  ) {\n    if (browserResource[SEMRESATTRS_PROCESS_RUNTIME_VERSION] === '') {\n      diag.debug(\n        'BrowserDetector failed: Unable to find required browser resources. '\n      );\n      return Resource.empty();\n    } else {\n      return new Resource({\n        ...browserResource,\n      });\n    }\n  }\n}\n\nexport const browserDetectorSync = new BrowserDetectorSync();\n"]}