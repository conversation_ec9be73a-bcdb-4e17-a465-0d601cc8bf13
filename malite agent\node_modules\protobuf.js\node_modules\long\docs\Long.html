<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Class: Long</title>
    
    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="scripts/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-esoccer.css">
</head>

<body>

<div id="main">
    
    <h1 class="page-title">Class: Long</h1>
    
    



<section>
    
<header>
    <h2>
    Long
    </h2>
    
        <div class="class-description">A Long class for representing a 64-bit two's-complement integer value.</div>
    
</header>  

<article>
    <div class="container-overview">
    
    
    
        
<dt>
    <h4 class="name" id="Long"><span class="type-signature"></span>new Long<span class="signature">(low, <span class="optional">high</span>, <span class="optional">unsigned</span>)</span><span class="type-signature"></span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Constructs a 64-bit two's-complement integer, given its low and high 32-bit
values as *signed* integers.  See the from* functions below for more
convenient ways of constructing Longs.

The internal representation of a long is the two given signed, 32-bit values.
We use 32-bit pieces because these are the size of integers on which
Javascript performs bit-operations.  For operations like addition and
multiplication, we split each number into 16-bit pieces, which can easily be
multiplied within Javascript's floating-point representation without overflow
or change in sign.

In the algorithms below, we frequently reduce the negative case to the
positive case by negating the input(s) and then post-processing the result.
Note that we must ALWAYS check specially whether those values are MIN_VALUE
(-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as
a positive number, it overflows back into a negative).  Not handling this
case would often result in infinite recursion.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		<th>Argument</th>
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>low</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>
|

<span class="param-type">!{low: number, high: number, unsigned: boolean}</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The low (signed) 32 bits of the long.
 Optionally accepts a Long-like object as the first parameter.</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>high</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The high (signed) 32 bits of the long.</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>unsigned</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">Whether unsigned or not. Defaults to `false` (signed).</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    
    
</dd>

    
    </div>
    
    
    
    
    
    
    
    
    
    
    
    
        <h3 class="subsection-title">Members</h3>
        
        <dl>
            
<dt>
    <h4 class="name" id="MAX_SIGNED_VALUE"><span class="type-signature">&lt;static> </span>MAX_SIGNED_VALUE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="MAX_UNSIGNED_VALUE"><span class="type-signature">&lt;static> </span>MAX_UNSIGNED_VALUE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="MAX_VALUE"><span class="type-signature">&lt;static> </span>MAX_VALUE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    <div class="description">
        Alias of <a href="Long.html#MAX_SIGNED_VALUE">Long.MAX_SIGNED_VALUE</a> for goog.math.Long compatibility.
    </div>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="MIN_SIGNED_VALUE"><span class="type-signature">&lt;static> </span>MIN_SIGNED_VALUE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="MIN_UNSIGNED_VALUE"><span class="type-signature">&lt;static> </span>MIN_UNSIGNED_VALUE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="MIN_VALUE"><span class="type-signature">&lt;static> </span>MIN_VALUE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    <div class="description">
        Alias of <a href="Long.html#MIN_SIGNED_VALUE">Long.MIN_SIGNED_VALUE</a>  for goog.math.Long compatibility.
    </div>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="NEG_ONE"><span class="type-signature">&lt;static> </span>NEG_ONE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="ONE"><span class="type-signature">&lt;static> </span>ONE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="UONE"><span class="type-signature">&lt;static> </span>UONE<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="UZERO"><span class="type-signature">&lt;static> </span>UZERO<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="ZERO"><span class="type-signature">&lt;static> </span>ZERO<span class="type-signature"> :<a href="Long.html">Long</a></span></h4>
    
    
</dt>
<dd>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="high"><span class="type-signature"></span>high<span class="type-signature"> :number</span></h4>
    
    
</dt>
<dd>
    
    <div class="description">
        The high 32 bits as a signed value.
    </div>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="low"><span class="type-signature"></span>low<span class="type-signature"> :number</span></h4>
    
    
</dt>
<dd>
    
    <div class="description">
        The low 32 bits as a signed value.
    </div>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        
            
<dt>
    <h4 class="name" id="unsigned"><span class="type-signature"></span>unsigned<span class="type-signature"> :boolean</span></h4>
    
    
</dt>
<dd>
    
    <div class="description">
        Whether unsigned or not.
    </div>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
</dd>

        </dl>
    
    
    
        <h3 class="subsection-title">Methods</h3>
        
        <dl>
            
<dt>
    <h4 class="name" id="from28Bits"><span class="type-signature">&lt;static> </span>from28Bits<span class="signature">(part0, part1, part2, <span class="optional">unsigned</span>)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns a Long representing the 64bit integer that comes by concatenating the given low, middle and high bits.
 Each is assumed to use 28 bits.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		<th>Argument</th>
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>part0</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The low 28 bits</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>part1</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The middle 28 bits</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>part2</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The high 28 (8) bits</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>unsigned</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">Whether unsigned or not. Defaults to false (signed).</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            


<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="fromBits"><span class="type-signature">&lt;static> </span>fromBits<span class="signature">(lowBits, highBits, <span class="optional">unsigned</span>)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns a Long representing the 64bit integer that comes by concatenating the given low and high bits. Each is
 assumed to use 32 bits.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		<th>Argument</th>
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>lowBits</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The low 32 bits.</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>highBits</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The high 32 bits.</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>unsigned</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">Whether unsigned or not. Defaults to false (signed).</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The corresponding Long value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="fromInt"><span class="type-signature">&lt;static> </span>fromInt<span class="signature">(value, <span class="optional">unsigned</span>)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns a Long representing the given (32-bit) integer value.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		<th>Argument</th>
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>value</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The 32-bit integer in question.</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>unsigned</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">Whether unsigned or not. Defaults to false (signed).</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The corresponding Long value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="fromNumber"><span class="type-signature">&lt;static> </span>fromNumber<span class="signature">(value, <span class="optional">unsigned</span>)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns a Long representing the given value, provided that it is a finite
number.  Otherwise, zero is returned.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		<th>Argument</th>
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>value</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The number in question.</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>unsigned</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">Whether unsigned or not. Defaults to false (signed).</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The corresponding Long value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="fromString"><span class="type-signature">&lt;static> </span>fromString<span class="signature">(str, <span class="optional">unsigned</span>, <span class="optional">radix</span>)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns a Long representation of the given string, written using the given
radix.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		<th>Argument</th>
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>str</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>
            
            
                <td class="attributes">
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The textual representation of the Long.</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>unsigned</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">boolean</span>
|

<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">Whether unsigned or not. Defaults to false (signed).</td>
        </tr>
	
	
	
        <tr>
            
                <td class="name"><code>radix</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The radix in which the text is written.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The corresponding Long value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="add"><span class="type-signature"></span>add<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns the sum of this and the given Long.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to add to this one.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The sum of this and the given Long.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="and"><span class="type-signature"></span>and<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns the bitwise-AND of this Long and the given one.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">The Long with which to AND.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The bitwise-AND of this and the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="clone"><span class="type-signature"></span>clone<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Cloned instance with the same low/high bits and unsigned flag.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="compare"><span class="type-signature"></span>compare<span class="signature">(other)</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Compares this Long with the given one.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to compare against.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    0 if they are the same, 1 if the this is greater, and -1
    if the given one is greater.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="div"><span class="type-signature"></span>div<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns this Long divided by the given one.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long by which to divide.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    This Long divided by the given one.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="equals"><span class="type-signature"></span>equals<span class="signature">(other)</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to compare against.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this Long equals the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="getHighBits"><span class="type-signature"></span>getHighBits<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The high 32 bits as a signed value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="getHighBitsUnsigned"><span class="type-signature"></span>getHighBitsUnsigned<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The high 32 bits as an unsigned value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="getLowBits"><span class="type-signature"></span>getLowBits<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The low 32 bits as a signed value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="getLowBitsUnsigned"><span class="type-signature"></span>getLowBitsUnsigned<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The low 32 bits as an unsigned value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="getNumBitsAbs"><span class="type-signature"></span>getNumBitsAbs<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Returns the number of bits needed to represent the absolute
    value of this Long.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="greaterThan"><span class="type-signature"></span>greaterThan<span class="signature">(other)</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to compare against.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this Long is greater than the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="greaterThanOrEqual"><span class="type-signature"></span>greaterThanOrEqual<span class="signature">(other)</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to compare against.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this Long is greater than or equal to the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="isEven"><span class="type-signature"></span>isEven<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this value is even.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="isNegative"><span class="type-signature"></span>isNegative<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this value is negative.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="isOdd"><span class="type-signature"></span>isOdd<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this value is odd.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="isZero"><span class="type-signature"></span>isZero<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this value is zero.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="lessThan"><span class="type-signature"></span>lessThan<span class="signature">(other)</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to compare against.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this Long is less than the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="lessThanOrEqual"><span class="type-signature"></span>lessThanOrEqual<span class="signature">(other)</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to compare against.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this Long is less than or equal to the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="modulo"><span class="type-signature"></span>modulo<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns this Long modulo the given one.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long by which to mod.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    This Long modulo the given one.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="multiply"><span class="type-signature"></span>multiply<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns the product of this and the given long.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to multiply with this.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The product of this and the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="negate"><span class="type-signature"></span>negate<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The negation of this value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="not"><span class="type-signature"></span>not<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The bitwise-NOT of this value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="notEquals"><span class="type-signature"></span>notEquals<span class="signature">(other)</span><span class="type-signature"> &rarr; {boolean}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to compare against.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Whether this Long does not equal the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">boolean</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="or"><span class="type-signature"></span>or<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns the bitwise-OR of this Long and the given one.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">The Long with which to OR.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The bitwise-OR of this and the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="shiftLeft"><span class="type-signature"></span>shiftLeft<span class="signature">(numBits)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns this Long with bits shifted to the left by the given amount.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>numBits</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
            
            
            
            <td class="description last">The number of bits by which to shift.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    This shifted to the left by the given amount.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="shiftRight"><span class="type-signature"></span>shiftRight<span class="signature">(numBits)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns this Long with bits shifted to the right by the given amount.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>numBits</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
            
            
            
            <td class="description last">The number of bits by which to shift.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    This shifted to the right by the given amount.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="shiftRightUnsigned"><span class="type-signature"></span>shiftRightUnsigned<span class="signature">(numBits)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns this Long with bits shifted to the right by the given amount, with
the new top bits matching the current sign bit.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>numBits</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
            
            
            
            <td class="description last">The number of bits by which to shift.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    This shifted to the right by the given amount, with
    zeros placed into the new leading bits.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="subtract"><span class="type-signature"></span>subtract<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns the difference of this and the given Long.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">Long to subtract from this.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The difference of this and the given Long.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="toInt"><span class="type-signature"></span>toInt<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The value, assuming it is a 32-bit integer.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="toNumber"><span class="type-signature"></span>toNumber<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The closest floating-point representation to this value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">number</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="toSigned"><span class="type-signature"></span>toSigned<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Signed long
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="toString"><span class="type-signature"></span>toString<span class="signature">(<span class="optional">radix</span>)</span><span class="type-signature"> &rarr; {string}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		<th>Argument</th>
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>radix</code></td>
            
            
            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>
            
            
                <td class="attributes">
                
                    &lt;optional><br>
                
                    
                
                </td>
            
            
            
            
            <td class="description last">The radix in which the text should be written.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The textual representation of this value.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type">string</span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="toUnsigned"><span class="type-signature"></span>toUnsigned<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    

    
    
    
    
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    Unsigned long
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        
            
<dt>
    <h4 class="name" id="xor"><span class="type-signature"></span>xor<span class="signature">(other)</span><span class="type-signature"> &rarr; {<a href="Long.html">Long</a>}</span></h4>
    
    
</dt>
<dd>
    
    
    <div class="description">
        Returns the bitwise-XOR of this Long and the given one.
    </div>
    

    
    
    
    
    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
	<tr>
		
		<th>Name</th>
		
		
		<th>Type</th>
		
		
		
		
		
		<th class="last">Description</th>
	</tr>
	</thead>
	
	<tbody>
	
	
        <tr>
            
                <td class="name"><code>other</code></td>
            
            
            <td class="type">
            
                
<span class="param-type"><a href="Long.html">Long</a></span>


            
            </td>
            
            
            
            
            
            <td class="description last">The Long with which to XOR.</td>
        </tr>
	
	
	</tbody>
</table>
    
    
    
<dl class="details">
    
        
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
</dl>

    
    
    
    
    
    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    The bitwise-XOR of this and the other.
</div>



<dl>
	<dt>
		Type
	</dt>
	<dd>
		
<span class="param-type"><a href="Long.html">Long</a></span>


	</dd>
</dl>

        
    
    
</dd>

        </dl>
    
    
    
    
    
</article>

</section>  




</div>

<nav>
    <nav-inner>
        <h2><a href="index.html">Index</a></h2><h3>Classes</h3><ul><li><a href="Long.html">Long</a></li></ul>
    </nav-inner>
</nav>

<br clear="both">

<script> prettyPrint(); </script>
</body>
</html>
