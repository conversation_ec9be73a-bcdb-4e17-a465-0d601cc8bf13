{"version": 3, "file": "EnvDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/EnvDetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,8CAA6C;AAC7C,8EAA+E;AAC/E,0CAAuC;AAKvC;;;GAGG;AACH,MAAM,eAAe;IAArB;QACE,+EAA+E;QAC9D,gBAAW,GAAG,GAAG,CAAC;QAEnC,oEAAoE;QACnD,qBAAgB,GAAG,GAAG,CAAC;QAExC,qEAAqE;QACpD,8BAAyB,GAAG,GAAG,CAAC;QAEhC,iCAA4B,GAC3C,uEAAuE;YACvE,IAAI,CAAC,WAAW;YAChB,cAAc,CAAC;QAEA,iCAA4B,GAC3C,oDAAoD;YACpD,IAAI,CAAC,WAAW;YAChB,cAAc,CAAC;IA+GnB,CAAC;IA7GC;;;;;;OAMG;IACH,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAuB,EAAE,CAAC;QAC1C,MAAM,GAAG,GAAG,IAAA,aAAM,GAAE,CAAC;QAErB,MAAM,aAAa,GAAG,GAAG,CAAC,wBAAwB,CAAC;QACnD,MAAM,WAAW,GAAG,GAAG,CAAC,iBAAiB,CAAC;QAE1C,IAAI,aAAa,EAAE;YACjB,IAAI;gBACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;gBACtE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;aAC7C;YAAC,OAAO,CAAC,EAAE;gBACV,UAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;aAChD;SACF;QAED,IAAI,WAAW,EAAE;YACf,UAAU,CAAC,+CAAwB,CAAC,GAAG,WAAW,CAAC;SACpD;QAED,OAAO,IAAI,mBAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACK,wBAAwB,CAC9B,gBAAyB;QAEzB,IAAI,CAAC,gBAAgB;YAAE,OAAO,EAAE,CAAC;QAEjC,MAAM,UAAU,GAAuB,EAAE,CAAC;QAC1C,MAAM,aAAa,GAAa,gBAAgB,CAAC,KAAK,CACpD,IAAI,CAAC,gBAAgB,EACrB,CAAC,CAAC,CACH,CAAC;QACF,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,MAAM,YAAY,GAAa,YAAY,CAAC,KAAK,CAC/C,IAAI,CAAC,yBAAyB,EAC9B,CAAC,CAAC,CACH,CAAC;YACF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,SAAS;aACV;YACD,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC;YAChC,gDAAgD;YAChD,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACjB,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE;gBAClC,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAC,4BAA4B,EAAE,CAAC,CAAC;aACvE;YACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,CAAC,4BAA4B,EAAE,CAAC,CAAC;aACzE;YACD,UAAU,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAC7C;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACK,QAAQ,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED,4CAA4C;IACpC,qBAAqB,CAAC,GAAW;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE;gBACvE,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACK,mBAAmB,CAAC,GAAW;QACrC,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;CACF;AAEY,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv } from '@opentelemetry/core';\nimport { SEMRESATTRS_SERVICE_NAME } from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../Resource';\nimport { DetectorSync, ResourceAttributes } from '../types';\nimport { ResourceDetectionConfig } from '../config';\nimport { IResource } from '../IResource';\n\n/**\n * EnvDetectorSync can be used to detect the presence of and create a Resource\n * from the OTEL_RESOURCE_ATTRIBUTES environment variable.\n */\nclass EnvDetectorSync implements DetectorSync {\n  // Type, attribute keys, and attribute values should not exceed 256 characters.\n  private readonly _MAX_LENGTH = 255;\n\n  // OTEL_RESOURCE_ATTRIBUTES is a comma-separated list of attributes.\n  private readonly _COMMA_SEPARATOR = ',';\n\n  // OTEL_RESOURCE_ATTRIBUTES contains key value pair separated by '='.\n  private readonly _LABEL_KEY_VALUE_SPLITTER = '=';\n\n  private readonly _ERROR_MESSAGE_INVALID_CHARS =\n    'should be a ASCII string with a length greater than 0 and not exceed ' +\n    this._MAX_LENGTH +\n    ' characters.';\n\n  private readonly _ERROR_MESSAGE_INVALID_VALUE =\n    'should be a ASCII string with a length not exceed ' +\n    this._MAX_LENGTH +\n    ' characters.';\n\n  /**\n   * Returns a {@link Resource} populated with attributes from the\n   * OTEL_RESOURCE_ATTRIBUTES environment variable. Note this is an async\n   * function to conform to the Detector interface.\n   *\n   * @param config The resource detection config\n   */\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes: ResourceAttributes = {};\n    const env = getEnv();\n\n    const rawAttributes = env.OTEL_RESOURCE_ATTRIBUTES;\n    const serviceName = env.OTEL_SERVICE_NAME;\n\n    if (rawAttributes) {\n      try {\n        const parsedAttributes = this._parseResourceAttributes(rawAttributes);\n        Object.assign(attributes, parsedAttributes);\n      } catch (e) {\n        diag.debug(`EnvDetector failed: ${e.message}`);\n      }\n    }\n\n    if (serviceName) {\n      attributes[SEMRESATTRS_SERVICE_NAME] = serviceName;\n    }\n\n    return new Resource(attributes);\n  }\n\n  /**\n   * Creates an attribute map from the OTEL_RESOURCE_ATTRIBUTES environment\n   * variable.\n   *\n   * OTEL_RESOURCE_ATTRIBUTES: A comma-separated list of attributes describing\n   * the source in more detail, e.g. “key1=val1,key2=val2”. Domain names and\n   * paths are accepted as attribute keys. Values may be quoted or unquoted in\n   * general. If a value contains whitespace, =, or \" characters, it must\n   * always be quoted.\n   *\n   * @param rawEnvAttributes The resource attributes as a comma-separated list\n   * of key/value pairs.\n   * @returns The sanitized resource attributes.\n   */\n  private _parseResourceAttributes(\n    rawEnvAttributes?: string\n  ): ResourceAttributes {\n    if (!rawEnvAttributes) return {};\n\n    const attributes: ResourceAttributes = {};\n    const rawAttributes: string[] = rawEnvAttributes.split(\n      this._COMMA_SEPARATOR,\n      -1\n    );\n    for (const rawAttribute of rawAttributes) {\n      const keyValuePair: string[] = rawAttribute.split(\n        this._LABEL_KEY_VALUE_SPLITTER,\n        -1\n      );\n      if (keyValuePair.length !== 2) {\n        continue;\n      }\n      let [key, value] = keyValuePair;\n      // Leading and trailing whitespaces are trimmed.\n      key = key.trim();\n      value = value.trim().split(/^\"|\"$/).join('');\n      if (!this._isValidAndNotEmpty(key)) {\n        throw new Error(`Attribute key ${this._ERROR_MESSAGE_INVALID_CHARS}`);\n      }\n      if (!this._isValid(value)) {\n        throw new Error(`Attribute value ${this._ERROR_MESSAGE_INVALID_VALUE}`);\n      }\n      attributes[key] = decodeURIComponent(value);\n    }\n    return attributes;\n  }\n\n  /**\n   * Determines whether the given String is a valid printable ASCII string with\n   * a length not exceed _MAX_LENGTH characters.\n   *\n   * @param str The String to be validated.\n   * @returns Whether the String is valid.\n   */\n  private _isValid(name: string): boolean {\n    return name.length <= this._MAX_LENGTH && this._isBaggageOctetString(name);\n  }\n\n  // https://www.w3.org/TR/baggage/#definition\n  private _isBaggageOctetString(str: string): boolean {\n    for (let i = 0; i < str.length; i++) {\n      const ch = str.charCodeAt(i);\n      if (ch < 0x21 || ch === 0x2c || ch === 0x3b || ch === 0x5c || ch > 0x7e) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Determines whether the given String is a valid printable ASCII string with\n   * a length greater than 0 and not exceed _MAX_LENGTH characters.\n   *\n   * @param str The String to be validated.\n   * @returns Whether the String is valid and not empty.\n   */\n  private _isValidAndNotEmpty(str: string): boolean {\n    return str.length > 0 && this._isValid(str);\n  }\n}\n\nexport const envDetectorSync = new EnvDetectorSync();\n"]}